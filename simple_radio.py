#!/usr/bin/env python3
"""
Eenvoudige Radio - Direct afstemmen op 96.8 MHz
Minimaal script voor SI4703 FM radio module
"""

import time
import sys
from pathlib import Path

# Voeg src directory toe aan Python path
sys.path.append(str(Path(__file__).parent / 'src'))

try:
    import RPi.GPIO as GPIO
    import smbus2
except ImportError as e:
    print(f"Fout: Kan benodigde library niet importeren: {e}")
    print("Installeer met: pip install RPi.GPIO smbus2")
    sys.exit(1)

class SimpleRadio:
    """Eenvoudige SI4703 radio klasse"""
    
    # SI4703 I2C adres
    SI4703_ADDR = 0x10
    
    # Register adressen
    POWERCFG = 0x02
    CHANNEL = 0x03
    SYSCONFIG1 = 0x04
    SYSCONFIG2 = 0x05
    STATUSRSSI = 0x0A
    
    def __init__(self, rst_pin=18, sdio_pin=2):
        """Initialiseer radio met reset en SDIO pinnen"""
        self.rst_pin = rst_pin
        self.sdio_pin = sdio_pin
        self.bus = None
        
        # Setup GPIO
        GPIO.setmode(GPIO.BCM)
        GPIO.setup(self.rst_pin, GPIO.OUT)
        GPIO.setup(self.sdio_pin, GPIO.OUT)
        
        print("🎵 Eenvoudige Radio geïnitialiseerd")
    
    def initialize(self):
        """Initialiseer de SI4703 chip"""
        try:
            print("Resetten van SI4703...")
            
            # Reset sequence
            GPIO.output(self.sdio_pin, GPIO.LOW)
            GPIO.output(self.rst_pin, GPIO.LOW)
            time.sleep(0.1)
            GPIO.output(self.rst_pin, GPIO.HIGH)
            time.sleep(0.1)
            
            # Switch naar I2C modus
            GPIO.setup(self.sdio_pin, GPIO.IN, pull_up_down=GPIO.PUD_UP)
            
            # Open I2C bus
            self.bus = smbus2.SMBus(1)
            time.sleep(0.1)
            
            # Power up de chip
            print("SI4703 wordt ingeschakeld...")
            self._write_register(self.POWERCFG, 0x4001)  # Enable + Power up
            time.sleep(0.5)
            
            # Configureer voor stereo ontvangst
            self._write_register(self.SYSCONFIG1, 0x1000)  # RDS enable
            self._write_register(self.SYSCONFIG2, 0x0F10)  # Volume = 15 (max)
            
            print("✅ SI4703 succesvol geïnitialiseerd")
            return True
            
        except Exception as e:
            print(f"❌ Fout bij initialiseren: {e}")
            return False
    
    def _write_register(self, reg, value):
        """Schrijf naar SI4703 register"""
        try:
            # SI4703 gebruikt 16-bit registers, big endian
            high_byte = (value >> 8) & 0xFF
            low_byte = value & 0xFF
            self.bus.write_i2c_block_data(self.SI4703_ADDR, reg, [high_byte, low_byte])
            time.sleep(0.01)  # Korte delay
        except Exception as e:
            print(f"Fout bij schrijven naar register {reg:02X}: {e}")
    
    def _read_register(self, reg):
        """Lees van SI4703 register"""
        try:
            data = self.bus.read_i2c_block_data(self.SI4703_ADDR, reg, 2)
            return (data[0] << 8) | data[1]
        except Exception as e:
            print(f"Fout bij lezen van register {reg:02X}: {e}")
            return 0
    
    def set_frequency(self, freq_mhz):
        """Stel frequentie in (87.5 - 108.0 MHz)"""
        if freq_mhz < 87.5 or freq_mhz > 108.0:
            print(f"❌ Ongeldige frequentie: {freq_mhz} MHz")
            return False
        
        try:
            print(f"📻 Afstemmen op {freq_mhz} MHz...")
            
            # Bereken channel waarde
            # Channel = (Freq - 87.5) / 0.2
            channel = int((freq_mhz - 87.5) / 0.2)
            
            # Schrijf channel naar register met TUNE bit
            channel_reg = (channel << 6) | 0x8000  # TUNE bit = 1
            self._write_register(self.CHANNEL, channel_reg)
            
            # Wacht tot tuning compleet is
            print("Wachten op tuning...")
            for i in range(50):  # Max 5 seconden wachten
                status = self._read_register(self.STATUSRSSI)
                if status & 0x4000:  # STC bit (Seek/Tune Complete)
                    break
                time.sleep(0.1)
            
            # Clear TUNE bit
            self._write_register(self.CHANNEL, channel << 6)
            
            print(f"✅ Afgestemd op {freq_mhz} MHz")
            return True
            
        except Exception as e:
            print(f"❌ Fout bij afstemmen: {e}")
            return False
    
    def get_signal_strength(self):
        """Krijg signaalsterkte (0-75)"""
        try:
            status = self._read_register(self.STATUSRSSI)
            rssi = status & 0x00FF
            return rssi
        except:
            return 0
    
    def cleanup(self):
        """Ruim GPIO op"""
        try:
            if self.bus:
                self.bus.close()
            GPIO.cleanup()
            print("🔌 GPIO opgeruimd")
        except:
            pass

def main():
    """Hoofdfunctie - start radio op 96.8 MHz"""
    print("🎵 Eenvoudige Radio - 96.8 MHz")
    print("=" * 35)
    
    radio = SimpleRadio()
    
    try:
        # Initialiseer radio
        if not radio.initialize():
            print("❌ Kan radio niet initialiseren")
            return
        
        # Stem af op 96.8 MHz
        if not radio.set_frequency(96.8):
            print("❌ Kan niet afstemmen op 96.8 MHz")
            return
        
        # Toon signaalsterkte
        signal = radio.get_signal_strength()
        print(f"📶 Signaalsterkte: {signal}/75")
        
        print("\n🎧 Radio is nu afgestemd op 96.8 MHz")
        print("   Sluit je koptelefoon aan en luister!")
        print("   Druk Ctrl+C om te stoppen")
        
        # Blijf draaien tot gebruiker stopt
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n\n👋 Radio wordt gestopt...")
    except Exception as e:
        print(f"❌ Onverwachte fout: {e}")
    finally:
        radio.cleanup()

if __name__ == "__main__":
    main()
